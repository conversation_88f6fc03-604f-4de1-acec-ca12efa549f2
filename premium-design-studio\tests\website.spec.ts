import { test, expect } from '@playwright/test';

test.describe('Nexus Studio Website', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });

  test('should load the homepage successfully', async ({ page }) => {
    // Check if the page loads with correct title
    await expect(page).toHaveTitle('Nexus Studio - Premium Design & Development');
    
    // Check if main heading is visible
    await expect(page.getByRole('heading', { name: 'Crafting Digital Excellence' })).toBeVisible();
    
    // Check if navigation is present
    await expect(page.getByRole('navigation')).toBeVisible();
    await expect(page.getByRole('navigation').getByRole('link', { name: 'Services' })).toBeVisible();
    await expect(page.getByRole('navigation').getByRole('link', { name: 'Work' })).toBeVisible();
    await expect(page.getByRole('navigation').getByRole('link', { name: 'About' })).toBeVisible();
    await expect(page.getByRole('navigation').getByRole('link', { name: 'Contact' })).toBeVisible();
  });

  test('should navigate to sections correctly', async ({ page }) => {
    // Test Services navigation
    await page.getByRole('navigation').getByRole('link', { name: 'Services' }).click();
    await expect(page).toHaveURL('http://localhost:3000/#services');

    // Test Work navigation
    await page.getByRole('navigation').getByRole('link', { name: 'Work' }).click();
    await expect(page).toHaveURL('http://localhost:3000/#work');

    // Test About navigation
    await page.getByRole('navigation').getByRole('link', { name: 'About' }).click();
    await expect(page).toHaveURL('http://localhost:3000/#about');

    // Test Contact navigation
    await page.getByRole('navigation').getByRole('link', { name: 'Contact' }).click();
    await expect(page).toHaveURL('http://localhost:3000/#contact');
  });

  test('should display all main sections', async ({ page }) => {
    // Check Services section
    await expect(page.getByRole('heading', { name: 'Our Services' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'UI/UX Design' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Development' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Digital Strategy' })).toBeVisible();
    
    // Check Work section
    await expect(page.getByRole('heading', { name: 'Featured Work' })).toBeVisible();
    
    // Check About section
    await expect(page.getByRole('heading', { name: 'About Nexus' })).toBeVisible();
    
    // Check Contact section
    await expect(page.getByRole('heading', { name: 'Let\'s Create Together' })).toBeVisible();
  });

  test('should have working buttons', async ({ page }) => {
    // Check if CTA buttons are present and clickable
    const startProjectButtons = page.getByRole('button', { name: 'Start Your Project' });
    await expect(startProjectButtons.first()).toBeVisible();
    
    const getStartedButton = page.getByRole('button', { name: 'Get Started' });
    await expect(getStartedButton).toBeVisible();
    
    const viewWorkButton = page.getByRole('button', { name: 'View Our Work' });
    await expect(viewWorkButton).toBeVisible();
  });

  test('should have working email link', async ({ page }) => {
    const emailLink = page.getByRole('link', { name: '<EMAIL>' });
    await expect(emailLink).toBeVisible();
    await expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  test('should display portfolio projects', async ({ page }) => {
    // Check if portfolio projects are visible
    await expect(page.getByRole('heading', { name: 'E-Commerce Platform' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Mobile Banking App' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'SaaS Dashboard' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Brand Identity' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'AI Platform' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Healthcare App' })).toBeVisible();
  });

  test('should have responsive design elements', async ({ page }) => {
    // Test on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.getByRole('heading', { name: 'Crafting Digital Excellence' })).toBeVisible();

    // Check mobile menu button is visible
    await expect(page.getByRole('button').filter({ hasText: /menu/i }).or(page.locator('button').filter({ has: page.locator('svg') }))).toBeVisible();

    // Test on tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.getByRole('heading', { name: 'Crafting Digital Excellence' })).toBeVisible();

    // Test on desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.getByRole('heading', { name: 'Crafting Digital Excellence' })).toBeVisible();
    await expect(page.getByRole('navigation').getByRole('link', { name: 'Services' })).toBeVisible();
  });

  test('should have footer with links', async ({ page }) => {
    // Check footer content
    await expect(page.getByRole('contentinfo')).toBeVisible();
    await expect(page.getByText('© 2024 Nexus Studio. All rights reserved.')).toBeVisible();
    
    // Check footer navigation links
    await expect(page.getByRole('link', { name: 'UI/UX Design' }).last()).toBeVisible();
    await expect(page.getByRole('link', { name: 'Web Development' }).last()).toBeVisible();
    await expect(page.getByRole('link', { name: 'About Us' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Contact' }).last()).toBeVisible();
  });
});
