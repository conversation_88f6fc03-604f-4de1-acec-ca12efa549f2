{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cc/premium-design-studio/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, useScroll, useTransform } from \"framer-motion\";\nimport { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Zap, Palette, <PERSON>, <PERSON>, Star } from \"lucide-react\";\nimport { useRef } from \"react\";\n\nexport default function Home() {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: containerRef,\n    offset: [\"start start\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [\"0%\", \"50%\"]);\n  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);\n\n  return (\n    <div ref={containerRef} className=\"relative\">\n      {/* Navigation */}\n      <motion.nav\n        initial={{ y: -100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.8, ease: \"easeOut\" }}\n        className=\"fixed top-0 left-0 right-0 z-50 glass border-b border-white/10\"\n      >\n        <div className=\"max-w-7xl mx-auto px-6 py-4 flex items-center justify-between\">\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"text-2xl font-bold gradient-text\"\n          >\n            NEXUS\n          </motion.div>\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {[\"Work\", \"Services\", \"About\", \"Contact\"].map((item, index) => (\n              <motion.a\n                key={item}\n                href={`#${item.toLowerCase()}`}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 * index, duration: 0.5 }}\n                whileHover={{ scale: 1.1, color: \"#6366f1\" }}\n                className=\"text-white/80 hover:text-white transition-colors\"\n              >\n                {item}\n              </motion.a>\n            ))}\n          </div>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-2 rounded-full text-white font-medium glow-hover\"\n          >\n            Get Started\n          </motion.button>\n        </div>\n      </motion.nav>\n\n      {/* Hero Section */}\n      <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n        {/* Animated Background */}\n        <div className=\"absolute inset-0\">\n          <motion.div\n            style={{ y, opacity }}\n            className=\"absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-purple-900/20 to-pink-900/20\"\n          />\n          {/* Floating Elements */}\n          {[...Array(20)].map((_, i) => (\n            <motion.div\n              key={i}\n              initial={{\n                x: Math.random() * window.innerWidth,\n                y: Math.random() * window.innerHeight,\n                scale: 0\n              }}\n              animate={{\n                scale: [0, 1, 0],\n                rotate: [0, 180, 360]\n              }}\n              transition={{\n                duration: Math.random() * 10 + 10,\n                repeat: Infinity,\n                delay: Math.random() * 5\n              }}\n              className=\"absolute w-2 h-2 bg-indigo-400 rounded-full opacity-30\"\n            />\n          ))}\n        </div>\n\n        <div className=\"relative z-10 text-center max-w-6xl mx-auto px-6\">\n          <motion.div\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ duration: 1, ease: \"easeOut\" }}\n            className=\"mb-8\"\n          >\n            <Sparkles className=\"w-16 h-16 mx-auto mb-6 text-indigo-400 animate-pulse-glow\" />\n          </motion.div>\n\n          <motion.h1\n            initial={{ y: 50, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-6xl md:text-8xl font-bold mb-6 leading-tight\"\n          >\n            <span className=\"gradient-text\">Crafting</span>\n            <br />\n            <span className=\"text-white\">Digital</span>\n            <br />\n            <span className=\"gradient-text\">Excellence</span>\n          </motion.h1>\n\n          <motion.p\n            initial={{ y: 30, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            We transform visionary ideas into extraordinary digital experiences through\n            innovative design, cutting-edge development, and unparalleled creativity.\n          </motion.p>\n\n          <motion.div\n            initial={{ y: 30, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05, boxShadow: \"0 0 30px rgba(99, 102, 241, 0.5)\" }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-8 py-4 rounded-full text-white font-semibold text-lg flex items-center gap-3 glow\"\n            >\n              Start Your Project\n              <ArrowRight className=\"w-5 h-5\" />\n            </motion.button>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"glass px-8 py-4 rounded-full text-white font-semibold text-lg border border-white/20 hover:border-white/40 transition-colors\"\n            >\n              View Our Work\n            </motion.button>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 1.5 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-white/60 rounded-full mt-2\"\n            />\n          </motion.div>\n        </motion.div>\n      </section>\n\n      {/* Services Section */}\n      <section id=\"services\" className=\"py-32 relative\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-20\"\n          >\n            <h2 className=\"text-5xl md:text-6xl font-bold mb-6\">\n              <span className=\"gradient-text\">Our</span> Services\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              We offer comprehensive digital solutions that elevate your brand and drive results\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              {\n                icon: Palette,\n                title: \"UI/UX Design\",\n                description: \"Creating intuitive and visually stunning user experiences that captivate and convert.\",\n                features: [\"User Research\", \"Wireframing\", \"Prototyping\", \"Visual Design\"]\n              },\n              {\n                icon: Code,\n                title: \"Development\",\n                description: \"Building robust, scalable applications with cutting-edge technologies and best practices.\",\n                features: [\"Frontend Development\", \"Backend Systems\", \"Mobile Apps\", \"Performance Optimization\"]\n              },\n              {\n                icon: Zap,\n                title: \"Digital Strategy\",\n                description: \"Comprehensive digital strategies that align with your business goals and market demands.\",\n                features: [\"Brand Strategy\", \"Market Analysis\", \"Growth Planning\", \"Digital Transformation\"]\n              }\n            ].map((service, index) => (\n              <motion.div\n                key={service.title}\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: index * 0.2 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10, scale: 1.02 }}\n                className=\"glass p-8 rounded-2xl border border-white/10 hover:border-white/20 transition-all duration-300 group\"\n              >\n                <div className=\"mb-6\">\n                  <service.icon className=\"w-12 h-12 text-indigo-400 group-hover:text-indigo-300 transition-colors\" />\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 text-white group-hover:gradient-text transition-all\">\n                  {service.title}\n                </h3>\n                <p className=\"text-white/80 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n                <ul className=\"space-y-2\">\n                  {service.features.map((feature) => (\n                    <li key={feature} className=\"flex items-center text-white/70\">\n                      <Star className=\"w-4 h-4 text-indigo-400 mr-3\" />\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Portfolio Section */}\n      <section id=\"work\" className=\"py-32 relative\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-20\"\n          >\n            <h2 className=\"text-5xl md:text-6xl font-bold mb-6\">\n              Featured <span className=\"gradient-text\">Work</span>\n            </h2>\n            <p className=\"text-xl text-white/80 max-w-3xl mx-auto\">\n              Discover our latest projects that showcase innovation, creativity, and technical excellence\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                title: \"E-Commerce Platform\",\n                category: \"Web Development\",\n                description: \"A modern e-commerce solution with advanced features and seamless user experience.\",\n                color: \"from-purple-500 to-pink-500\"\n              },\n              {\n                title: \"Mobile Banking App\",\n                category: \"Mobile Design\",\n                description: \"Intuitive banking application with focus on security and user-friendly interface.\",\n                color: \"from-blue-500 to-cyan-500\"\n              },\n              {\n                title: \"SaaS Dashboard\",\n                category: \"UI/UX Design\",\n                description: \"Comprehensive dashboard design for a B2B SaaS platform with complex data visualization.\",\n                color: \"from-green-500 to-emerald-500\"\n              },\n              {\n                title: \"Brand Identity\",\n                category: \"Branding\",\n                description: \"Complete brand identity design including logo, guidelines, and marketing materials.\",\n                color: \"from-orange-500 to-red-500\"\n              },\n              {\n                title: \"AI Platform\",\n                category: \"Web Development\",\n                description: \"Cutting-edge AI platform with machine learning capabilities and modern interface.\",\n                color: \"from-indigo-500 to-purple-500\"\n              },\n              {\n                title: \"Healthcare App\",\n                category: \"Mobile Development\",\n                description: \"Telemedicine application connecting patients with healthcare providers seamlessly.\",\n                color: \"from-teal-500 to-blue-500\"\n              }\n            ].map((project, index) => (\n              <motion.div\n                key={project.title}\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -10, scale: 1.02 }}\n                className=\"group cursor-pointer\"\n              >\n                <div className=\"glass rounded-2xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300\">\n                  <div className={`h-48 bg-gradient-to-br ${project.color} relative overflow-hidden`}>\n                    <motion.div\n                      whileHover={{ scale: 1.1 }}\n                      transition={{ duration: 0.3 }}\n                      className=\"absolute inset-0 bg-black/20\"\n                    />\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <motion.div\n                        whileHover={{ rotate: 360 }}\n                        transition={{ duration: 0.5 }}\n                        className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\"\n                      >\n                        <ArrowRight className=\"w-8 h-8 text-white\" />\n                      </motion.div>\n                    </div>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"text-sm text-indigo-400 mb-2\">{project.category}</div>\n                    <h3 className=\"text-xl font-bold mb-3 text-white group-hover:gradient-text transition-all\">\n                      {project.title}\n                    </h3>\n                    <p className=\"text-white/70 leading-relaxed\">\n                      {project.description}\n                    </p>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-32 relative\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n            >\n              <h2 className=\"text-5xl md:text-6xl font-bold mb-8\">\n                About <span className=\"gradient-text\">Nexus</span>\n              </h2>\n              <p className=\"text-xl text-white/80 mb-8 leading-relaxed\">\n                We are a team of passionate designers, developers, and strategists who believe\n                in the power of exceptional digital experiences. With over a decade of combined\n                experience, we've helped hundreds of businesses transform their digital presence.\n              </p>\n              <div className=\"grid grid-cols-2 gap-8 mb-8\">\n                {[\n                  { number: \"150+\", label: \"Projects Completed\" },\n                  { number: \"50+\", label: \"Happy Clients\" },\n                  { number: \"10+\", label: \"Years Experience\" },\n                  { number: \"24/7\", label: \"Support Available\" }\n                ].map((stat, index) => (\n                  <motion.div\n                    key={stat.label}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"text-center\"\n                  >\n                    <div className=\"text-3xl font-bold gradient-text mb-2\">{stat.number}</div>\n                    <div className=\"text-white/70\">{stat.label}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"glass p-8 rounded-2xl border border-white/10\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  {[...Array(12)].map((_, i) => (\n                    <motion.div\n                      key={i}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      whileInView={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.5, delay: i * 0.05 }}\n                      viewport={{ once: true }}\n                      whileHover={{ scale: 1.05 }}\n                      className={`h-16 rounded-lg bg-gradient-to-br ${\n                        i % 4 === 0 ? 'from-purple-500 to-pink-500' :\n                        i % 4 === 1 ? 'from-blue-500 to-cyan-500' :\n                        i % 4 === 2 ? 'from-green-500 to-emerald-500' :\n                        'from-orange-500 to-red-500'\n                      } opacity-80 hover:opacity-100 transition-opacity`}\n                    />\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-32 relative\">\n        <div className=\"max-w-4xl mx-auto px-6 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-5xl md:text-6xl font-bold mb-8\">\n              Let's <span className=\"gradient-text\">Create</span> Together\n            </h2>\n            <p className=\"text-xl text-white/80 mb-12 max-w-2xl mx-auto leading-relaxed\">\n              Ready to transform your digital presence? Let's discuss your project and\n              bring your vision to life with our expertise and creativity.\n            </p>\n\n            <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n              {[\n                { icon: Users, title: \"Consultation\", description: \"Free initial consultation to understand your needs\" },\n                { icon: Palette, title: \"Design\", description: \"Custom design tailored to your brand and goals\" },\n                { icon: Zap, title: \"Launch\", description: \"Fast delivery and ongoing support for your success\" }\n              ].map((step, index) => (\n                <motion.div\n                  key={step.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.2 }}\n                  viewport={{ once: true }}\n                  className=\"glass p-6 rounded-xl border border-white/10\"\n                >\n                  <step.icon className=\"w-12 h-12 text-indigo-400 mx-auto mb-4\" />\n                  <h3 className=\"text-xl font-bold mb-3 text-white\">{step.title}</h3>\n                  <p className=\"text-white/70\">{step.description}</p>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n            >\n              <motion.button\n                whileHover={{ scale: 1.05, boxShadow: \"0 0 30px rgba(99, 102, 241, 0.5)\" }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-gradient-to-r from-indigo-500 to-purple-600 px-8 py-4 rounded-full text-white font-semibold text-lg flex items-center gap-3 glow\"\n              >\n                Start Your Project\n                <ArrowRight className=\"w-5 h-5\" />\n              </motion.button>\n\n              <motion.a\n                href=\"mailto:<EMAIL>\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"glass px-8 py-4 rounded-full text-white font-semibold text-lg border border-white/20 hover:border-white/40 transition-colors\"\n              >\n                <EMAIL>\n              </motion.a>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-16 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-6\">\n          <div className=\"grid md:grid-cols-4 gap-8 mb-12\">\n            <div>\n              <div className=\"text-2xl font-bold gradient-text mb-4\">NEXUS</div>\n              <p className=\"text-white/70 leading-relaxed\">\n                Crafting exceptional digital experiences through innovative design and development.\n              </p>\n            </div>\n\n            {[\n              {\n                title: \"Services\",\n                links: [\"UI/UX Design\", \"Web Development\", \"Mobile Apps\", \"Branding\"]\n              },\n              {\n                title: \"Company\",\n                links: [\"About Us\", \"Our Work\", \"Careers\", \"Contact\"]\n              },\n              {\n                title: \"Connect\",\n                links: [\"Twitter\", \"LinkedIn\", \"Instagram\", \"Dribbble\"]\n              }\n            ].map((column) => (\n              <div key={column.title}>\n                <h3 className=\"text-white font-semibold mb-4\">{column.title}</h3>\n                <ul className=\"space-y-2\">\n                  {column.links.map((link) => (\n                    <li key={link}>\n                      <a href=\"#\" className=\"text-white/70 hover:text-white transition-colors\">\n                        {link}\n                      </a>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"border-t border-white/10 pt-8 text-center text-white/70\">\n            <p>&copy; 2024 Nexus Studio. All rights reserved. Crafted with passion and precision.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IAC7D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAE9D,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;;0BAEhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC/B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;gBAC7C,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAQ;gCAAY;gCAAS;6BAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM;wCAAO,UAAU;oCAAI;oCAChD,YAAY;wCAAE,OAAO;wCAAK,OAAO;oCAAU;oCAC3C,WAAU;8CAET;mCARI;;;;;;;;;;sCAYX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,OAAO;oCAAE;oCAAG;gCAAQ;gCACpB,WAAU;;;;;;4BAGX;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;wCACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;wCACrC,OAAO;oCACT;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;wCAChB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;oCACvB;oCACA,YAAY;wCACV,UAAU,KAAK,MAAM,KAAK,KAAK;wCAC/B,QAAQ;wCACR,OAAO,KAAK,MAAM,KAAK;oCACzB;oCACA,WAAU;mCAfL;;;;;;;;;;;kCAoBX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAa;;;;;;kDAC7B,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CACX;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;4CAAM,WAAW;wCAAmC;wCACzE,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAU;;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM,wMAAA,CAAA,UAAO;oCACb,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAiB;wCAAe;wCAAe;qCAAgB;gCAC5E;gCACA;oCACE,MAAM,kMAAA,CAAA,OAAI;oCACV,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAwB;wCAAmB;wCAAe;qCAA2B;gCAClG;gCACA;oCACE,MAAM,gMAAA,CAAA,MAAG;oCACT,OAAO;oCACP,aAAa;oCACb,UAAU;wCAAC;wCAAkB;wCAAmB;wCAAmB;qCAAyB;gCAC9F;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;wCAAI,OAAO;oCAAK;oCAClC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE1B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf;;mDAFM;;;;;;;;;;;mCAnBR,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAgC5B,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAAsC;sDACzC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAE3C,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,OAAO;oCACP,UAAU;oCACV,aAAa;oCACb,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;wCAAI,OAAO;oCAAK;oCAClC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,uBAAuB,EAAE,QAAQ,KAAK,CAAC,yBAAyB,CAAC;;kEAChF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,OAAO;wDAAI;wDACzB,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,QAAQ;4DAAI;4DAC1B,YAAY;gEAAE,UAAU;4DAAI;4DAC5B,WAAU;sEAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAI5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgC,QAAQ,QAAQ;;;;;;kEAC/D,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;mCA/BrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BA0C5B,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;;4CAAsC;0DAC5C,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAExC,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,QAAQ;gDAAQ,OAAO;4CAAqB;4CAC9C;gDAAE,QAAQ;gDAAO,OAAO;4CAAgB;4CACxC;gDAAE,QAAQ;gDAAO,OAAO;4CAAmB;4CAC3C;gDAAE,QAAQ;gDAAQ,OAAO;4CAAoB;yCAC9C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEAAyC,KAAK,MAAM;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;;+CARrC,KAAK,KAAK;;;;;;;;;;;;;;;;0CAcvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,YAAY;oDAAE,UAAU;oDAAK,OAAO,IAAI;gDAAK;gDAC7C,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,WAAW,CAAC,kCAAkC,EAC5C,IAAI,MAAM,IAAI,gCACd,IAAI,MAAM,IAAI,8BACd,IAAI,MAAM,IAAI,kCACd,6BACD,gDAAgD,CAAC;+CAX7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsBrB,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;;oCAAsC;kDAC5C,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CAAgE;;;;;;0CAK7E,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM,oMAAA,CAAA,QAAK;wCAAE,OAAO;wCAAgB,aAAa;oCAAqD;oCACxG;wCAAE,MAAM,wMAAA,CAAA,UAAO;wCAAE,OAAO;wCAAU,aAAa;oCAAiD;oCAChG;wCAAE,MAAM,gMAAA,CAAA,MAAG;wCAAE,OAAO;wCAAU,aAAa;oCAAqD;iCACjG,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqC,KAAK,KAAK;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;0DAAiB,KAAK,WAAW;;;;;;;uCATzC,KAAK,KAAK;;;;;;;;;;0CAcrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;4CAAM,WAAW;wCAAmC;wCACzE,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAK;wCACL,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;gCAK9C;oCACC;wCACE,OAAO;wCACP,OAAO;4CAAC;4CAAgB;4CAAmB;4CAAe;yCAAW;oCACvE;oCACA;wCACE,OAAO;wCACP,OAAO;4CAAC;4CAAY;4CAAY;4CAAW;yCAAU;oCACvD;oCACA;wCACE,OAAO;wCACP,OAAO;4CAAC;4CAAW;4CAAY;4CAAa;yCAAW;oCACzD;iCACD,CAAC,GAAG,CAAC,CAAC,uBACL,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC,OAAO,KAAK;;;;;;0DAC3D,8OAAC;gDAAG,WAAU;0DACX,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;kEACC,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEACnB;;;;;;uDAFI;;;;;;;;;;;uCAJL,OAAO,KAAK;;;;;;;;;;;sCAe1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}