"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Zap, Palette, Code, Users, Star, Menu, X } from "lucide-react";
import { useRef, useState, useEffect } from "react";

export default function Home() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [floatingElements, setFloatingElements] = useState<Array<{x: number, y: number, delay: number, duration: number}>>([]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  useEffect(() => {
    setMounted(true);
    // Generate floating elements only on client side
    const elements = [...Array(20)].map(() => ({
      x: Math.random() * 1200,
      y: Math.random() * 800,
      delay: Math.random() * 5,
      duration: Math.random() * 10 + 10
    }));
    setFloatingElements(elements);
  }, []);

  return (
    <div ref={containerRef} className="relative min-h-screen">
      {/* Navigation */}
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="fixed top-0 left-0 right-0 z-50 glass border-b border-white/10"
      >
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="text-2xl font-bold gradient-text"
          >
            NEXUS
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {["Work", "Services", "About", "Contact"].map((item, index) => (
              <motion.a
                key={item}
                href={`#${item.toLowerCase()}`}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.5 }}
                whileHover={{ scale: 1.1, color: "#6366f1" }}
                className="text-white/80 hover:text-white transition-colors"
              >
                {item}
              </motion.a>
            ))}
          </div>

          {/* Desktop CTA Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="hidden md:block bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-2 rounded-full text-white font-medium glow-hover"
          >
            Get Started
          </motion.button>

          {/* Mobile Menu Button */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden p-2 text-white"
          >
            {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </motion.button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden glass border-t border-white/10"
          >
            <div className="max-w-7xl mx-auto px-6 py-4 space-y-4">
              {["Work", "Services", "About", "Contact"].map((item) => (
                <a
                  key={item}
                  href={`#${item.toLowerCase()}`}
                  onClick={() => setMobileMenuOpen(false)}
                  className="block text-white/80 hover:text-white transition-colors py-2"
                >
                  {item}
                </a>
              ))}
              <button className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-3 rounded-full text-white font-medium mt-4">
                Get Started
              </button>
            </div>
          </motion.div>
        )}
      </motion.nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <motion.div
            style={{ y, opacity }}
            className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-purple-900/20 to-pink-900/20"
          />
          {/* Floating Elements */}
          {mounted && floatingElements.map((element, i) => (
            <motion.div
              key={i}
              initial={{
                x: element.x,
                y: element.y,
                scale: 0
              }}
              animate={{
                scale: [0, 1, 0],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: element.duration,
                repeat: Infinity,
                delay: element.delay
              }}
              className="absolute w-2 h-2 bg-indigo-400 rounded-full opacity-30"
            />
          ))}
        </div>

        <div className="relative z-10 text-center max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="mb-8"
          >
            <Sparkles className="w-16 h-16 mx-auto mb-6 text-indigo-400 animate-pulse-glow" />
          </motion.div>

          <motion.h1
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-6xl md:text-8xl font-bold mb-6 leading-tight"
          >
            <span className="gradient-text">Crafting</span>
            <br />
            <span className="text-white">Digital</span>
            <br />
            <span className="gradient-text">Excellence</span>
          </motion.h1>

          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            We transform visionary ideas into extraordinary digital experiences through
            innovative design, cutting-edge development, and unparalleled creativity.
          </motion.p>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.button
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(99, 102, 241, 0.5)" }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-indigo-500 to-purple-600 px-8 py-4 rounded-full text-white font-semibold text-lg flex items-center gap-3 glow"
            >
              Start Your Project
              <ArrowRight className="w-5 h-5" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="glass px-8 py-4 rounded-full text-white font-semibold text-lg border border-white/20 hover:border-white/40 transition-colors"
            >
              View Our Work
            </motion.button>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-white/60 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-32 relative">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="gradient-text">Our</span> Services
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              We offer comprehensive digital solutions that elevate your brand and drive results
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Palette,
                title: "UI/UX Design",
                description: "Creating intuitive and visually stunning user experiences that captivate and convert.",
                features: ["User Research", "Wireframing", "Prototyping", "Visual Design"]
              },
              {
                icon: Code,
                title: "Development",
                description: "Building robust, scalable applications with cutting-edge technologies and best practices.",
                features: ["Frontend Development", "Backend Systems", "Mobile Apps", "Performance Optimization"]
              },
              {
                icon: Zap,
                title: "Digital Strategy",
                description: "Comprehensive digital strategies that align with your business goals and market demands.",
                features: ["Brand Strategy", "Market Analysis", "Growth Planning", "Digital Transformation"]
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="glass p-8 rounded-2xl border border-white/10 hover:border-white/20 transition-all duration-300 group"
              >
                <div className="mb-6">
                  <service.icon className="w-12 h-12 text-indigo-400 group-hover:text-indigo-300 transition-colors" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-white group-hover:gradient-text transition-all">
                  {service.title}
                </h3>
                <p className="text-white/80 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-white/70">
                      <Star className="w-4 h-4 text-indigo-400 mr-3" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="work" className="py-32 relative">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-6xl font-bold mb-6">
              Featured <span className="gradient-text">Work</span>
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Discover our latest projects that showcase innovation, creativity, and technical excellence
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "E-Commerce Platform",
                category: "Web Development",
                description: "A modern e-commerce solution with advanced features and seamless user experience.",
                color: "from-purple-500 to-pink-500"
              },
              {
                title: "Mobile Banking App",
                category: "Mobile Design",
                description: "Intuitive banking application with focus on security and user-friendly interface.",
                color: "from-blue-500 to-cyan-500"
              },
              {
                title: "SaaS Dashboard",
                category: "UI/UX Design",
                description: "Comprehensive dashboard design for a B2B SaaS platform with complex data visualization.",
                color: "from-green-500 to-emerald-500"
              },
              {
                title: "Brand Identity",
                category: "Branding",
                description: "Complete brand identity design including logo, guidelines, and marketing materials.",
                color: "from-orange-500 to-red-500"
              },
              {
                title: "AI Platform",
                category: "Web Development",
                description: "Cutting-edge AI platform with machine learning capabilities and modern interface.",
                color: "from-indigo-500 to-purple-500"
              },
              {
                title: "Healthcare App",
                category: "Mobile Development",
                description: "Telemedicine application connecting patients with healthcare providers seamlessly.",
                color: "from-teal-500 to-blue-500"
              }
            ].map((project, index) => (
              <motion.div
                key={project.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group cursor-pointer"
              >
                <div className="glass rounded-2xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300">
                  <div className={`h-48 bg-gradient-to-br ${project.color} relative overflow-hidden`}>
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.3 }}
                      className="absolute inset-0 bg-black/20"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.5 }}
                        className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <ArrowRight className="w-8 h-8 text-white" />
                      </motion.div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="text-sm text-indigo-400 mb-2">{project.category}</div>
                    <h3 className="text-xl font-bold mb-3 text-white group-hover:gradient-text transition-all">
                      {project.title}
                    </h3>
                    <p className="text-white/70 leading-relaxed">
                      {project.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-32 relative">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-5xl md:text-6xl font-bold mb-8">
                About <span className="gradient-text">Nexus</span>
              </h2>
              <p className="text-xl text-white/80 mb-8 leading-relaxed">
                We are a team of passionate designers, developers, and strategists who believe
                in the power of exceptional digital experiences. With over a decade of combined
                experience, we've helped hundreds of businesses transform their digital presence.
              </p>
              <div className="grid grid-cols-2 gap-8 mb-8">
                {[
                  { number: "150+", label: "Projects Completed" },
                  { number: "50+", label: "Happy Clients" },
                  { number: "10+", label: "Years Experience" },
                  { number: "24/7", label: "Support Available" }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center"
                  >
                    <div className="text-3xl font-bold gradient-text mb-2">{stat.number}</div>
                    <div className="text-white/70">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="glass p-8 rounded-2xl border border-white/10">
                <div className="grid grid-cols-2 gap-4">
                  {[...Array(12)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: i * 0.05 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.05 }}
                      className={`h-16 rounded-lg bg-gradient-to-br ${
                        i % 4 === 0 ? 'from-purple-500 to-pink-500' :
                        i % 4 === 1 ? 'from-blue-500 to-cyan-500' :
                        i % 4 === 2 ? 'from-green-500 to-emerald-500' :
                        'from-orange-500 to-red-500'
                      } opacity-80 hover:opacity-100 transition-opacity`}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-32 relative">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-5xl md:text-6xl font-bold mb-8">
              Let's <span className="gradient-text">Create</span> Together
            </h2>
            <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto leading-relaxed">
              Ready to transform your digital presence? Let's discuss your project and
              bring your vision to life with our expertise and creativity.
            </p>

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {[
                { icon: Users, title: "Consultation", description: "Free initial consultation to understand your needs" },
                { icon: Palette, title: "Design", description: "Custom design tailored to your brand and goals" },
                { icon: Zap, title: "Launch", description: "Fast delivery and ongoing support for your success" }
              ].map((step, index) => (
                <motion.div
                  key={step.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="glass p-6 rounded-xl border border-white/10"
                >
                  <step.icon className="w-12 h-12 text-indigo-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold mb-3 text-white">{step.title}</h3>
                  <p className="text-white/70">{step.description}</p>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(99, 102, 241, 0.5)" }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-indigo-500 to-purple-600 px-8 py-4 rounded-full text-white font-semibold text-lg flex items-center gap-3 glow"
              >
                Start Your Project
                <ArrowRight className="w-5 h-5" />
              </motion.button>

              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="glass px-8 py-4 rounded-full text-white font-semibold text-lg border border-white/20 hover:border-white/40 transition-colors"
              >
                <EMAIL>
              </motion.a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 border-t border-white/10">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div>
              <div className="text-2xl font-bold gradient-text mb-4">NEXUS</div>
              <p className="text-white/70 leading-relaxed">
                Crafting exceptional digital experiences through innovative design and development.
              </p>
            </div>

            {[
              {
                title: "Services",
                links: ["UI/UX Design", "Web Development", "Mobile Apps", "Branding"]
              },
              {
                title: "Company",
                links: ["About Us", "Our Work", "Careers", "Contact"]
              },
              {
                title: "Connect",
                links: ["Twitter", "LinkedIn", "Instagram", "Dribbble"]
              }
            ].map((column) => (
              <div key={column.title}>
                <h3 className="text-white font-semibold mb-4">{column.title}</h3>
                <ul className="space-y-2">
                  {column.links.map((link) => (
                    <li key={link}>
                      <a href="#" className="text-white/70 hover:text-white transition-colors">
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-white/10 pt-8 text-center text-white/70">
            <p>&copy; 2024 Nexus Studio. All rights reserved. Crafted with passion and precision.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
