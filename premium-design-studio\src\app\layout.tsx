import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Nexus Studio - Premium Design & Development",
  description: "We craft exceptional digital experiences through innovative design and cutting-edge development. Transform your vision into reality with our premium creative solutions.",
  keywords: ["design studio", "web development", "UI/UX design", "premium design", "creative agency"],
  authors: [{ name: "Nexus Studio" }],
  creator: "Nexus Studio",
  openGraph: {
    title: "Nexus Studio - Premium Design & Development",
    description: "We craft exceptional digital experiences through innovative design and cutting-edge development.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Nexus Studio - Premium Design & Development",
    description: "We craft exceptional digital experiences through innovative design and cutting-edge development.",
  },
  robots: "index, follow",
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased bg-black text-white overflow-x-hidden`}
      >
        {children}
      </body>
    </html>
  );
}
