{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_5802845b-module__9kuUBG__className\",\n  \"variable\": \"inter_5802845b-module__9kuUBG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5802845b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_599c3ea9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_599c3ea9-module__MBI5IG__className\",\n  \"variable\": \"jetbrains_mono_599c3ea9-module__MBI5IG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_599c3ea9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22variable%22:%22--font-jetbrains-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cc/premium-design-studio/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  variable: \"--font-jetbrains-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Nexus Studio - Premium Design & Development\",\n  description: \"We craft exceptional digital experiences through innovative design and cutting-edge development. Transform your vision into reality with our premium creative solutions.\",\n  keywords: [\"design studio\", \"web development\", \"UI/UX design\", \"premium design\", \"creative agency\"],\n  authors: [{ name: \"Nexus Studio\" }],\n  creator: \"Nexus Studio\",\n  openGraph: {\n    title: \"Nexus Studio - Premium Design & Development\",\n    description: \"We craft exceptional digital experiences through innovative design and cutting-edge development.\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Nexus Studio - Premium Design & Development\",\n    description: \"We craft exceptional digital experiences through innovative design and cutting-edge development.\",\n  },\n  robots: \"index, follow\",\n};\n\nexport const viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased bg-black text-white overflow-x-hidden`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAiB;QAAmB;QAAgB;QAAkB;KAAkB;IACnG,SAAS;QAAC;YAAE,MAAM;QAAe;KAAE;IACnC,SAAS;IACT,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;AACV;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,4DAA4D,CAAC;sBAEnH;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cc/premium-design-studio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}